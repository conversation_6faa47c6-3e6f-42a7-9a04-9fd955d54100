import type { Express, Request } from "express";
import { createServer, type Server } from "http";
import { storage } from "./storage";
import {
  insertContactSubmissionSchema,
  insertBlogCategorySchema,
  insertBlogArticleSchema,
  updateBlogArticleSchema,
  blogArticleQuerySchema,
  type BlogArticleWithDetails,
  type BlogMarketType,
  type BlogLanguageType,
} from "@shared/schema";
import { z } from "zod";
import crypto from "crypto";
import bcrypt from "bcryptjs";
import { createClient } from "@supabase/supabase-js";
import { sendPasswordResetEmail } from "./emailService";
import { contentResolutionService } from "./contentResolutionService";
import { marketDetectionService } from "./marketDetectionService";
import rateLimit from "express-rate-limit";

// Extend Express Request type to include adminUser
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      adminUser?: {
        id: string;
        email: string;
        name: string;
      };
    }
  }
}

// Initialize Supabase client for server-side operations
const supabaseUrl =
  process.env.SUPABASE_URL || "https://anwefmklplkjxkmzpnva.supabase.co";
const supabaseServiceKey =
  process.env.SUPABASE_KEY ||
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImFud2VmbWtscGxranhrbXpwbnZhIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTczNzc5NjI0NiwiZXhwIjoyMDUzMzcyMjQ2fQ.XufCiYJLa531SyHDNkkVFNH0_rbPgZp4UG5yMGRabfs";
const supabase = createClient(supabaseUrl, supabaseServiceKey);

// Rate limiting for password reset requests
const passwordResetLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 3, // Limit each IP to 3 password reset requests per windowMs
  message: {
    message: "Too many password reset requests. Please try again later.",
  },
  standardHeaders: true,
  legacyHeaders: false,
});

// Utility functions for blog management
function generateSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^\w\s-]/g, "") // Remove special characters
    .replace(/\s+/g, "-") // Replace spaces with hyphens
    .replace(/-+/g, "-") // Replace multiple hyphens with single
    .trim();
}

function calculateReadingTime(content: string): number {
  // Remove HTML tags and count words
  const plainText = content.replace(/<[^>]*>/g, "");
  const wordCount = plainText
    .split(/\s+/)
    .filter((word) => word.length > 0).length;
  // Assume 200 words per minute reading speed
  return Math.max(1, Math.round(wordCount / 200));
}

function sanitizeHtml(content: string): string {
  // Enhanced HTML sanitization for server-side
  return content
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, "")
    .replace(/<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi, "")
    .replace(/<object\b[^<]*(?:(?!<\/object>)<[^<]*)*<\/object>/gi, "")
    .replace(/<embed\b[^<]*(?:(?!<\/embed>)<[^<]*)*<\/embed>/gi, "")
    .replace(/<form\b[^<]*(?:(?!<\/form>)<[^<]*)*<\/form>/gi, "")
    .replace(/on\w+="[^"]*"/gi, "") // Remove event handlers
    .replace(/javascript:/gi, "") // Remove javascript: URLs
    .replace(/data:(?!image\/)/gi, ""); // Remove data URLs except images
}

// Admin authentication middleware
async function requireAdminAuth(req: any, res: any, next: any) {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith("Bearer ")) {
      return res.status(401).json({ message: "Authentication required" });
    }

    const token = authHeader.substring(7);

    // Decode the token (same logic as in client)
    const decoded = atob(token);
    const [userId] = decoded.split(":");

    const { data: adminUser, error } = await supabase
      .from("website_admins")
      .select("id, email, name")
      .eq("id", userId)
      .eq("is_active", true)
      .single();

    if (error || !adminUser) {
      return res.status(401).json({ message: "Invalid authentication" });
    }

    req.adminUser = adminUser;
    next();
  } catch (error) {
    console.error("Auth middleware error:", error);
    return res.status(401).json({ message: "Authentication failed" });
  }
}

export async function registerRoutes(app: Express): Promise<Server> {
  // API routes

  // Health check endpoint
  app.get("/api/ping", (req, res) => {
    res.status(200).send("pong");
  });

  // Admin Password Reset Endpoints

  // Request password reset
  app.post(
    "/api/admin/forgot-password",
    passwordResetLimiter,
    async (req, res) => {
      try {
        const schema = z.object({
          email: z.string().email("Invalid email address"),
        });

        const { email } = schema.parse(req.body);

        // Check if admin user exists and is active
        const { data: adminUser, error: userError } = await supabase
          .from("website_admins")
          .select("id, email, name")
          .eq("email", email)
          .eq("is_active", "true")
          .single();

        if (userError || !adminUser) {
          // Don't reveal if user exists or not for security
          return res.status(200).json({
            message:
              "If an account with that email exists, a password reset link has been sent.",
          });
        }

        // Generate secure reset token
        const resetToken = crypto.randomBytes(32).toString("hex");
        const resetExpires = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes from now

        // Update user with reset token
        const { error: updateError } = await supabase
          .from("website_admins")
          .update({
            password_reset_token: resetToken,
            password_reset_expires: resetExpires.toISOString(),
            updated_at: new Date().toISOString(),
          })
          .eq("id", adminUser.id);

        if (updateError) {
          console.error("Error updating reset token:", updateError);
          return res.status(500).json({
            message: "Failed to process password reset request.",
          });
        }

        // Send password reset email
        const emailSent = await sendPasswordResetEmail(
          adminUser.name,
          adminUser.email,
          resetToken
        );

        if (!emailSent) {
          console.error("Failed to send password reset email");
          // Don't reveal email sending failure to prevent information disclosure
        }

        // Log token in development for testing
        if (process.env.NODE_ENV === "development") {
          console.log(
            `Password reset requested for ${email}, token: ${resetToken}`
          );
        }

        res.status(200).json({
          message:
            "If an account with that email exists, a password reset link has been sent.",
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            message: "Invalid email address",
            errors: error.format(),
          });
        }

        console.error("Error processing password reset request:", error);
        res.status(500).json({ message: "Internal server error" });
      }
    }
  );

  // Validate reset token
  app.get("/api/admin/validate-reset-token/:token", async (req, res) => {
    try {
      const { token } = req.params;

      if (!token) {
        return res.status(400).json({
          message: "Reset token is required",
        });
      }

      // Check if token exists and is not expired
      const { data: adminUser, error } = await supabase
        .from("website_admins")
        .select("id, email, password_reset_expires")
        .eq("password_reset_token", token)
        .eq("is_active", "true")
        .single();

      if (error || !adminUser) {
        return res.status(400).json({
          message: "Invalid or expired reset token",
        });
      }

      // Check if token has expired
      const now = new Date();
      const expiresAt = new Date(adminUser.password_reset_expires);

      if (now > expiresAt) {
        return res.status(400).json({
          message: "Reset token has expired",
        });
      }

      res.status(200).json({
        message: "Token is valid",
        email: adminUser.email,
      });
    } catch (error) {
      console.error("Error validating reset token:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Reset password
  app.post("/api/admin/reset-password", async (req, res) => {
    try {
      const schema = z.object({
        token: z
          .string()
          .min(64, "Invalid reset token")
          .max(64, "Invalid reset token"),
        newPassword: z
          .string()
          .min(8, "Password must be at least 8 characters")
          .max(128, "Password must be less than 128 characters")
          .regex(
            /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/,
            "Password must contain at least one uppercase letter, one lowercase letter, and one number"
          ),
      });

      const { token, newPassword } = schema.parse(req.body);

      // Validate token and get user
      const { data: adminUser, error } = await supabase
        .from("website_admins")
        .select("id, email, password_reset_expires")
        .eq("password_reset_token", token)
        .eq("is_active", "true")
        .single();

      if (error || !adminUser) {
        return res.status(400).json({
          message: "Invalid or expired reset token",
        });
      }

      // Check if token has expired
      const now = new Date();
      const expiresAt = new Date(adminUser.password_reset_expires);

      if (now > expiresAt) {
        return res.status(400).json({
          message: "Reset token has expired",
        });
      }

      // Hash the new password
      const saltRounds = 12;
      const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

      // Update password and clear reset token
      const { error: updateError } = await supabase
        .from("website_admins")
        .update({
          password_hash: hashedPassword,
          password_reset_token: null,
          password_reset_expires: null,
          updated_at: new Date().toISOString(),
        })
        .eq("id", adminUser.id);

      if (updateError) {
        console.error("Error updating password:", updateError);
        return res.status(500).json({
          message: "Failed to reset password",
        });
      }

      res.status(200).json({
        message: "Password reset successfully",
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: "Invalid input",
          errors: error.format(),
        });
      }

      console.error("Error resetting password:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Blog API Endpoints

  // Public blog endpoints

  // Get published blog articles (public)
  app.get("/api/blog/articles", async (req, res) => {
    try {
      const query = blogArticleQuerySchema.parse(req.query);

      let supabaseQuery = supabase
        .from("blog_articles")
        .select(
          `
          id,
          title,
          slug,
          summary,
          featured_image_url,
          read_time_minutes,
          published_at,
          created_at,
          blog_categories (
            id,
            name,
            slug,
            color
          ),
          website_admins (
            id,
            name
          )
        `
        )
        .eq("status", "published")
        .order("published_at", { ascending: false });

      // Apply filters
      if (query.categoryId) {
        supabaseQuery = supabaseQuery.eq("category_id", query.categoryId);
      }

      if (query.search) {
        supabaseQuery = supabaseQuery.ilike("title", `%${query.search}%`);
      }

      // Apply pagination
      const from = (query.page - 1) * query.limit;
      const to = from + query.limit - 1;
      supabaseQuery = supabaseQuery.range(from, to);

      const { data: articles, error, count } = await supabaseQuery;

      if (error) {
        console.error("Error fetching articles:", error);
        return res.status(500).json({ message: "Failed to fetch articles" });
      }

      res.json({
        articles: articles || [],
        pagination: {
          page: query.page,
          limit: query.limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / query.limit),
        },
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: "Invalid query parameters",
          errors: error.format(),
        });
      }

      console.error("Error in blog articles endpoint:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Get single published blog article by slug (public)
  app.get("/api/blog/articles/:slug", async (req, res) => {
    try {
      const { slug } = req.params;

      const { data: article, error } = await supabase
        .from("blog_articles")
        .select(
          `
          id,
          title,
          slug,
          summary,
          content,
          featured_image_url,
          meta_title,
          meta_description,
          read_time_minutes,
          published_at,
          created_at,
          blog_categories (
            id,
            name,
            slug,
            color
          ),
          website_admins (
            id,
            name
          )
        `
        )
        .eq("slug", slug)
        .eq("status", "published")
        .single();

      if (error || !article) {
        return res.status(404).json({ message: "Article not found" });
      }

      res.json({ article });
    } catch (error) {
      console.error("Error fetching article:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Get blog categories (public)
  app.get("/api/blog/categories", async (req, res) => {
    try {
      const { data: categories, error } = await supabase
        .from("blog_categories")
        .select("*")
        .order("name");

      if (error) {
        console.error("Error fetching categories:", error);
        return res.status(500).json({ message: "Failed to fetch categories" });
      }

      res.json({ categories: categories || [] });
    } catch (error) {
      console.error("Error in categories endpoint:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Contact form submission endpoint
  app.post("/api/contact", async (req, res) => {
    try {
      // Validate the request body
      const validatedData = insertContactSubmissionSchema.parse(req.body);

      // Store the contact submission
      const submission = await storage.createContactSubmission(validatedData);

      res.status(201).json({
        message: "Contact submission received",
        submission,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: "Invalid form data",
          errors: error.format(),
        });
      }

      console.error("Error processing contact submission:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Newsletter signup endpoint
  app.post("/api/newsletter", async (req, res) => {
    try {
      // Validate the email
      const schema = z.object({
        email: z.string().email("Invalid email address"),
      });

      const { email } = schema.parse(req.body);

      // Store the newsletter subscription
      const submission = await storage.createContactSubmission({ email });

      res.status(201).json({
        message: "Newsletter subscription successful",
        submission,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: "Invalid email address",
          errors: error.format(),
        });
      }

      console.error("Error processing newsletter signup:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Image management endpoints (protected)

  // Get all uploaded images for admin
  app.get("/api/admin/images", requireAdminAuth, async (req, res) => {
    try {
      const { data: files, error } = await supabase.storage
        .from("blog-images")
        .list("", {
          limit: 100,
          offset: 0,
          sortBy: { column: "created_at", order: "desc" },
        });

      if (error) {
        console.error("Error fetching images:", error);
        return res.status(500).json({ message: "Failed to fetch images" });
      }

      const images = files.map((file) => ({
        id: file.id || file.name,
        name: file.name,
        url: supabase.storage.from("blog-images").getPublicUrl(file.name).data
          .publicUrl,
        size: file.metadata?.size || 0,
        created_at: file.created_at,
        updated_at: file.updated_at,
      }));

      res.json({ images });
    } catch (error) {
      console.error("Error in images endpoint:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Delete an uploaded image
  app.delete(
    "/api/admin/images/:filename",
    requireAdminAuth,
    async (req, res) => {
      try {
        const { filename } = req.params;

        const { error } = await supabase.storage
          .from("blog-images")
          .remove([filename]);

        if (error) {
          console.error("Error deleting image:", error);
          return res.status(500).json({ message: "Failed to delete image" });
        }

        res.json({ message: "Image deleted successfully" });
      } catch (error) {
        console.error("Error in delete image endpoint:", error);
        res.status(500).json({ message: "Internal server error" });
      }
    }
  );

  // Bulk delete images
  app.post(
    "/api/admin/images/bulk-delete",
    requireAdminAuth,
    async (req, res) => {
      try {
        const { filenames } = req.body;

        if (!Array.isArray(filenames) || filenames.length === 0) {
          return res.status(400).json({ message: "Invalid filenames array" });
        }

        const { error } = await supabase.storage
          .from("blog-images")
          .remove(filenames);

        if (error) {
          console.error("Error bulk deleting images:", error);
          return res.status(500).json({ message: "Failed to delete images" });
        }

        res.json({
          message: `Successfully deleted ${filenames.length} images`,
          deleted: filenames.length,
        });
      } catch (error) {
        console.error("Error in bulk delete images endpoint:", error);
        res.status(500).json({ message: "Internal server error" });
      }
    }
  );

  // Admin blog endpoints (protected)

  // Get all blog articles for admin (with drafts)
  app.get("/api/admin/blog/articles", requireAdminAuth, async (req, res) => {
    try {
      const query = blogArticleQuerySchema.parse(req.query);

      let supabaseQuery = supabase
        .from("blog_articles")
        .select(
          `
          id,
          title,
          slug,
          summary,
          status,
          market,
          language,
          featured_image_url,
          read_time_minutes,
          published_at,
          created_at,
          updated_at,
          blog_categories (
            id,
            name,
            slug,
            color
          ),
          website_admins (
            id,
            name
          )
        `
        )
        .order("updated_at", { ascending: false });

      // Apply filters
      if (query.status) {
        supabaseQuery = supabaseQuery.eq("status", query.status);
      }

      if (query.categoryId) {
        supabaseQuery = supabaseQuery.eq("category_id", query.categoryId);
      }

      if (query.market) {
        supabaseQuery = supabaseQuery.eq("market", query.market);
      }

      if (query.language) {
        supabaseQuery = supabaseQuery.eq("language", query.language);
      }

      if (query.search) {
        supabaseQuery = supabaseQuery.ilike("title", `%${query.search}%`);
      }

      // Apply pagination
      const from = (query.page - 1) * query.limit;
      const to = from + query.limit - 1;
      supabaseQuery = supabaseQuery.range(from, to);

      const { data: articles, error, count } = await supabaseQuery;

      if (error) {
        console.error("Error fetching admin articles:", error);
        return res.status(500).json({ message: "Failed to fetch articles" });
      }

      res.json({
        articles: articles || [],
        pagination: {
          page: query.page,
          limit: query.limit,
          total: count || 0,
          totalPages: Math.ceil((count || 0) / query.limit),
        },
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: "Invalid query parameters",
          errors: error.format(),
        });
      }

      console.error("Error in admin articles endpoint:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Get single blog article for admin (including drafts)
  app.get(
    "/api/admin/blog/articles/:id",
    requireAdminAuth,
    async (req, res) => {
      try {
        const { id } = req.params;

        const { data: article, error } = await supabase
          .from("blog_articles")
          .select(
            `
          *,
          blog_categories (
            id,
            name,
            slug,
            color
          ),
          website_admins (
            id,
            name,
            email
          )
        `
          )
          .eq("id", id)
          .single();

        if (error || !article) {
          return res.status(404).json({ message: "Article not found" });
        }

        res.json({ article });
      } catch (error) {
        console.error("Error fetching admin article:", error);
        res.status(500).json({ message: "Internal server error" });
      }
    }
  );

  // Create new blog article
  app.post("/api/admin/blog/articles", requireAdminAuth, async (req, res) => {
    try {
      const validatedData = insertBlogArticleSchema.parse(req.body);
      const adminUser = req.adminUser;

      if (!adminUser) {
        return res.status(401).json({ message: "Authentication required" });
      }

      // Generate slug if not provided
      let slug = validatedData.slug || generateSlug(validatedData.title);

      // Ensure slug is unique
      const { data: existingArticle } = await supabase
        .from("blog_articles")
        .select("id")
        .eq("slug", slug)
        .single();

      if (existingArticle) {
        slug = `${slug}-${Date.now()}`;
      }

      // Sanitize content
      const sanitizedContent = sanitizeHtml(validatedData.content);

      // Calculate reading time
      const readTimeMinutes = calculateReadingTime(sanitizedContent);

      // Prepare article data
      const articleData = {
        ...validatedData,
        slug,
        content: sanitizedContent,
        author_id: adminUser.id,
        read_time_minutes: readTimeMinutes,
        published_at:
          validatedData.status === "published"
            ? new Date().toISOString()
            : null,
      };

      const { data: article, error } = await supabase
        .from("blog_articles")
        .insert([articleData])
        .select(
          `
          *,
          blog_categories (
            id,
            name,
            slug,
            color
          ),
          website_admins (
            id,
            name,
            email
          )
        `
        )
        .single();

      if (error) {
        console.error("Error creating article:", error);
        return res.status(500).json({ message: "Failed to create article" });
      }

      res.status(201).json({
        message: "Article created successfully",
        article,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: "Invalid article data",
          errors: error.format(),
        });
      }

      console.error("Error in create article endpoint:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Update blog article
  app.put(
    "/api/admin/blog/articles/:id",
    requireAdminAuth,
    async (req, res) => {
      try {
        const { id } = req.params;
        const validatedData = updateBlogArticleSchema.parse(req.body);

        // Check if article exists
        const { data: existingArticle, error: fetchError } = await supabase
          .from("blog_articles")
          .select("id, slug, status")
          .eq("id", id)
          .single();

        if (fetchError || !existingArticle) {
          return res.status(404).json({ message: "Article not found" });
        }

        // Handle slug update
        let slug = validatedData.slug;
        if (slug && slug !== existingArticle.slug) {
          // Check if new slug is unique
          const { data: slugExists } = await supabase
            .from("blog_articles")
            .select("id")
            .eq("slug", slug)
            .neq("id", id)
            .single();

          if (slugExists) {
            return res.status(400).json({ message: "Slug already exists" });
          }
        }

        // Prepare update data
        const updateData: any = { ...validatedData };

        // Sanitize content if provided
        if (validatedData.content) {
          updateData.content = sanitizeHtml(validatedData.content);
          updateData.read_time_minutes = calculateReadingTime(
            updateData.content
          );
        }

        // Handle publishing
        if (
          validatedData.status === "published" &&
          existingArticle.status !== "published"
        ) {
          updateData.published_at = new Date().toISOString();
        } else if (validatedData.status !== "published") {
          updateData.published_at = null;
        }

        updateData.updated_at = new Date().toISOString();

        const { data: article, error } = await supabase
          .from("blog_articles")
          .update(updateData)
          .eq("id", id)
          .select(
            `
          *,
          blog_categories (
            id,
            name,
            slug,
            color
          ),
          website_admins (
            id,
            name,
            email
          )
        `
          )
          .single();

        if (error) {
          console.error("Error updating article:", error);
          return res.status(500).json({ message: "Failed to update article" });
        }

        res.json({
          message: "Article updated successfully",
          article,
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            message: "Invalid article data",
            errors: error.format(),
          });
        }

        console.error("Error in update article endpoint:", error);
        res.status(500).json({ message: "Internal server error" });
      }
    }
  );

  // Delete blog article
  app.delete(
    "/api/admin/blog/articles/:id",
    requireAdminAuth,
    async (req, res) => {
      try {
        const { id } = req.params;

        const { error } = await supabase
          .from("blog_articles")
          .delete()
          .eq("id", id);

        if (error) {
          console.error("Error deleting article:", error);
          return res.status(500).json({ message: "Failed to delete article" });
        }

        res.json({ message: "Article deleted successfully" });
      } catch (error) {
        console.error("Error in delete article endpoint:", error);
        res.status(500).json({ message: "Internal server error" });
      }
    }
  );

  // Blog categories management endpoints

  // Get all categories for admin
  app.get("/api/admin/blog/categories", requireAdminAuth, async (req, res) => {
    try {
      const { data: categories, error } = await supabase
        .from("blog_categories")
        .select("*")
        .order("name");

      if (error) {
        console.error("Error fetching admin categories:", error);
        return res.status(500).json({ message: "Failed to fetch categories" });
      }

      res.json({ categories: categories || [] });
    } catch (error) {
      console.error("Error in admin categories endpoint:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Create new category
  app.post("/api/admin/blog/categories", requireAdminAuth, async (req, res) => {
    try {
      const validatedData = insertBlogCategorySchema.parse(req.body);

      // Generate slug if not provided
      const slug = validatedData.slug || generateSlug(validatedData.name);

      const categoryData = {
        ...validatedData,
        slug,
      };

      const { data: category, error } = await supabase
        .from("blog_categories")
        .insert([categoryData])
        .select()
        .single();

      if (error) {
        if (error.code === "23505") {
          // Unique constraint violation
          return res
            .status(400)
            .json({ message: "Category name or slug already exists" });
        }
        console.error("Error creating category:", error);
        return res.status(500).json({ message: "Failed to create category" });
      }

      res.status(201).json({
        message: "Category created successfully",
        category,
      });
    } catch (error) {
      if (error instanceof z.ZodError) {
        return res.status(400).json({
          message: "Invalid category data",
          errors: error.format(),
        });
      }

      console.error("Error in create category endpoint:", error);
      res.status(500).json({ message: "Internal server error" });
    }
  });

  // Update category
  app.put(
    "/api/admin/blog/categories/:id",
    requireAdminAuth,
    async (req, res) => {
      try {
        const { id } = req.params;
        const validatedData = insertBlogCategorySchema
          .partial()
          .parse(req.body);

        const { data: category, error } = await supabase
          .from("blog_categories")
          .update(validatedData)
          .eq("id", id)
          .select()
          .single();

        if (error) {
          if (error.code === "23505") {
            // Unique constraint violation
            return res
              .status(400)
              .json({ message: "Category name or slug already exists" });
          }
          console.error("Error updating category:", error);
          return res.status(500).json({ message: "Failed to update category" });
        }

        if (!category) {
          return res.status(404).json({ message: "Category not found" });
        }

        res.json({
          message: "Category updated successfully",
          category,
        });
      } catch (error) {
        if (error instanceof z.ZodError) {
          return res.status(400).json({
            message: "Invalid category data",
            errors: error.format(),
          });
        }

        console.error("Error in update category endpoint:", error);
        res.status(500).json({ message: "Internal server error" });
      }
    }
  );

  // Delete category
  app.delete(
    "/api/admin/blog/categories/:id",
    requireAdminAuth,
    async (req, res) => {
      try {
        const { id } = req.params;

        // Check if category is being used by any articles
        const { data: articlesUsingCategory, error: checkError } =
          await supabase
            .from("blog_articles")
            .select("id")
            .eq("category_id", id)
            .limit(1);

        if (checkError) {
          console.error("Error checking category usage:", checkError);
          return res
            .status(500)
            .json({ message: "Failed to check category usage" });
        }

        if (articlesUsingCategory && articlesUsingCategory.length > 0) {
          return res.status(400).json({
            message: "Cannot delete category that is being used by articles",
          });
        }

        const { error } = await supabase
          .from("blog_categories")
          .delete()
          .eq("id", id);

        if (error) {
          console.error("Error deleting category:", error);
          return res.status(500).json({ message: "Failed to delete category" });
        }

        res.json({ message: "Category deleted successfully" });
      } catch (error) {
        console.error("Error in delete category endpoint:", error);
        res.status(500).json({ message: "Internal server error" });
      }
    }
  );

  // Multi-Market Blog API Endpoints

  // Get available markets and languages
  app.get("/api/blog/markets", async (req, res) => {
    try {
      const markets = await contentResolutionService.getAvailableMarkets();
      res.json(markets);
    } catch (error) {
      console.error("Error fetching markets:", error);
      res.status(500).json({ message: "Failed to fetch markets" });
    }
  });

  // Get market-specific blog articles with smart fallbacks
  app.get("/api/blog/articles/market/:market", async (req, res) => {
    try {
      const { market } = req.params;
      const { language = "en", ...queryParams } = req.query;

      // Validate market and language
      if (!["global", "usa", "be-fr", "be-nl"].includes(market)) {
        return res.status(400).json({ message: "Invalid market" });
      }

      if (!["en", "fr", "nl"].includes(language as string)) {
        return res.status(400).json({ message: "Invalid language" });
      }

      // Parse query parameters
      const validatedQuery = blogArticleQuerySchema.parse(queryParams);

      // Resolve articles with market/language preferences
      const resolvedArticles =
        await contentResolutionService.resolveArticlesList({
          market: market as BlogMarketType,
          language: language as BlogLanguageType,
          status: validatedQuery.status || "published",
          categoryId: validatedQuery.categoryId,
          limit: validatedQuery.limit,
          offset: (validatedQuery.page - 1) * validatedQuery.limit,
        });

      // Format response
      const articles = resolvedArticles.map((resolved) => ({
        ...resolved.article,
        _resolution: {
          source: resolved.source,
          translationUsed: resolved.translationUsed || false,
        },
      }));

      res.json({
        articles,
        pagination: {
          page: validatedQuery.page,
          limit: validatedQuery.limit,
          total: articles.length, // TODO: Implement proper total count
        },
        market: {
          code: market,
          language: language,
          info: marketDetectionService.getMarketInfo(market as BlogMarketType),
        },
      });
    } catch (error) {
      console.error("Error fetching market articles:", error);
      res.status(500).json({ message: "Failed to fetch articles" });
    }
  });

  // Get single article with market/language resolution
  app.get("/api/blog/articles/market/:market/:slug", async (req, res) => {
    try {
      const { market, slug } = req.params;
      const { language = "en" } = req.query;

      // Validate market and language
      if (!["global", "usa", "be-fr", "be-nl"].includes(market)) {
        return res.status(400).json({ message: "Invalid market" });
      }

      if (!["en", "fr", "nl"].includes(language as string)) {
        return res.status(400).json({ message: "Invalid language" });
      }

      // Resolve article with smart fallbacks
      const resolved = await contentResolutionService.resolveArticle(slug, {
        market: market as BlogMarketType,
        language: language as BlogLanguageType,
      });

      if (!resolved) {
        return res.status(404).json({ message: "Article not found" });
      }

      res.json({
        ...resolved.article,
        _resolution: {
          source: resolved.source,
          translationUsed: resolved.translationUsed || false,
        },
        market: {
          code: market,
          language: language,
          info: marketDetectionService.getMarketInfo(market as BlogMarketType),
        },
      });
    } catch (error) {
      console.error("Error fetching market article:", error);
      res.status(500).json({ message: "Failed to fetch article" });
    }
  });

  // Auto-detect market and redirect
  app.get("/api/blog/detect-market", async (req, res) => {
    try {
      const detection = marketDetectionService.detectFromRequest(req);
      const marketInfo = marketDetectionService.getMarketInfo(detection.market);

      res.json({
        detected: detection,
        market: marketInfo,
        suggestedUrl: marketDetectionService.getCanonicalUrl(detection.market),
      });
    } catch (error) {
      console.error("Error detecting market:", error);
      res.status(500).json({ message: "Failed to detect market" });
    }
  });

  // Blog Translation Management API Endpoints (Admin only)

  // Get translations for blog articles
  app.get("/api/admin/translations", requireAdminAuth, async (req, res) => {
    try {
      const { filter, language } = req.query;

      let query = supabase.from("translation_keys").select(`
          *,
          translations (
            id,
            language,
            content,
            is_auto_translated,
            is_approved,
            confidence,
            created_at,
            updated_at
          )
        `);

      // Filter for blog-related translation keys
      if (filter) {
        query = query.like("key", filter as string);
      } else {
        query = query.like("key", "blog.article.%");
      }

      if (language) {
        query = query.eq("translations.language", language);
      }

      const { data: translationKeys, error } = await query;

      if (error) {
        console.error("Error fetching translations:", error);
        return res
          .status(500)
          .json({ message: "Failed to fetch translations" });
      }

      // Flatten the data structure
      const translations =
        translationKeys?.flatMap((key) =>
          key.translations.map((translation: any) => ({
            ...translation,
            translation_key: key,
          }))
        ) || [];

      res.json({ translations });
    } catch (error) {
      console.error("Error fetching translations:", error);
      res.status(500).json({ message: "Failed to fetch translations" });
    }
  });

  // Update a translation
  app.put("/api/admin/translations/:id", requireAdminAuth, async (req, res) => {
    try {
      const { id } = req.params;
      const { content, is_approved } = req.body;

      const { data: translation, error } = await supabase
        .from("translations")
        .update({
          content,
          is_approved,
          updated_at: new Date().toISOString(),
        })
        .eq("id", id)
        .select()
        .single();

      if (error) {
        console.error("Error updating translation:", error);
        return res
          .status(500)
          .json({ message: "Failed to update translation" });
      }

      res.json({
        message: "Translation updated successfully",
        translation,
      });
    } catch (error) {
      console.error("Error updating translation:", error);
      res.status(500).json({ message: "Failed to update translation" });
    }
  });

  // Approve a translation
  app.post(
    "/api/admin/translations/:id/approve",
    requireAdminAuth,
    async (req, res) => {
      try {
        const { id } = req.params;

        const { data: translation, error } = await supabase
          .from("translations")
          .update({
            is_approved: true,
            updated_at: new Date().toISOString(),
          })
          .eq("id", id)
          .select()
          .single();

        if (error) {
          console.error("Error approving translation:", error);
          return res
            .status(500)
            .json({ message: "Failed to approve translation" });
        }

        res.json({
          message: "Translation approved successfully",
          translation,
        });
      } catch (error) {
        console.error("Error approving translation:", error);
        res.status(500).json({ message: "Failed to approve translation" });
      }
    }
  );

  const httpServer = createServer(app);
  return httpServer;
}
