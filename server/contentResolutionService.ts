import { createClient } from "@supabase/supabase-js";
import type {
  BlogArticle,
  BlogMarketType,
  BlogLanguageType,
} from "@shared/schema";

const supabaseUrl = process.env.SUPABASE_URL!;
const supabaseKey = process.env.SUPABASE_KEY!;
const supabase = createClient(supabaseUrl, supabaseKey);

export interface ContentResolutionOptions {
  market: BlogMarketType;
  language: BlogLanguageType;
  fallbackToGlobal?: boolean;
  fallbackToEnglish?: boolean;
}

export interface ResolvedContent {
  article: BlogArticle;
  source: "exact" | "market-fallback" | "global-fallback" | "english-fallback";
  translationUsed?: boolean;
}

/**
 * Content Resolution Service
 * Handles smart content resolution with market/language fallbacks using existing translation system
 */
export class ContentResolutionService {
  /**
   * Resolve a single blog article with smart fallbacks
   */
  async resolveArticle(
    slug: string,
    options: ContentResolutionOptions
  ): Promise<ResolvedContent | null> {
    const {
      market,
      language,
      fallbackToGlobal = true,
      fallbackToEnglish = true,
    } = options;

    // Priority 1: Exact market + language match
    let article = await this.findArticle(slug, market, language);
    if (article) {
      return { article, source: "exact" };
    }

    // Priority 2: Market-specific article in default language for that market
    if (market !== "global") {
      const marketDefaultLang = await this.getMarketDefaultLanguage(market);
      if (marketDefaultLang && marketDefaultLang !== language) {
        article = await this.findArticle(slug, market, marketDefaultLang);
        if (article) {
          return { article, source: "market-fallback" };
        }
      }
    }

    // Priority 3: Global article in requested language
    if (fallbackToGlobal && market !== "global") {
      article = await this.findArticle(slug, "global", language);
      if (article) {
        return { article, source: "global-fallback" };
      }
    }

    // Priority 4: Global article in English (ultimate fallback)
    if (fallbackToEnglish && language !== "en") {
      article = await this.findArticle(slug, "global", "en");
      if (article) {
        return { article, source: "english-fallback" };
      }
    }

    // Priority 5: Check for translated content using translation_keys
    const translatedContent = await this.findTranslatedContent(slug, language);
    if (translatedContent) {
      return {
        article: translatedContent,
        source: "global-fallback",
        translationUsed: true,
      };
    }

    return null;
  }

  /**
   * Resolve multiple blog articles for a market/language
   */
  async resolveArticlesList(
    options: ContentResolutionOptions & {
      status?: "published" | "draft" | "archived";
      categoryId?: number;
      limit?: number;
      offset?: number;
    }
  ): Promise<ResolvedContent[]> {
    const {
      market,
      language,
      status = "published",
      categoryId,
      limit = 10,
      offset = 0,
      fallbackToGlobal = true,
    } = options;

    // Build query for market-specific articles
    let query = supabase
      .from("blog_articles")
      .select(
        `
        *,
        blog_categories (
          id,
          name,
          slug,
          color
        ),
        website_admins (
          id,
          name,
          email
        )
      `
      )
      .eq("status", status)
      .order("published_at", { ascending: false })
      .range(offset, offset + limit - 1);

    // Apply market and language filters
    if (market === "global") {
      query = query.eq("market", "global").eq("language", language);
    } else {
      // For specific markets, include both market-specific and global articles
      query = query.or(`market.eq.${market},market.eq.global`);
      if (fallbackToGlobal) {
        query = query.or(`language.eq.${language},language.eq.en`);
      } else {
        query = query.eq("language", language);
      }
    }

    if (categoryId) {
      query = query.eq("category_id", categoryId);
    }

    const { data: articles, error } = await query;

    if (error) {
      console.error("Error fetching articles:", error);
      return [];
    }

    if (!articles) return [];

    // Process articles with resolution logic
    const resolvedArticles: ResolvedContent[] = [];
    const seenSlugs = new Set<string>();

    for (const article of articles) {
      // Skip duplicates (prefer market-specific over global)
      if (seenSlugs.has(article.slug)) continue;
      seenSlugs.add(article.slug);

      let source: ResolvedContent["source"] = "exact";

      if (article.market === "global" && market !== "global") {
        source = "global-fallback";
      } else if (article.language !== language) {
        source = "market-fallback";
      }

      resolvedArticles.push({
        article,
        source,
      });
    }

    return resolvedArticles;
  }

  /**
   * Find a specific article by slug, market, and language
   */
  private async findArticle(
    slug: string,
    market: BlogMarketType,
    language: BlogLanguageType
  ): Promise<BlogArticle | null> {
    const { data: article, error } = await supabase
      .from("blog_articles")
      .select(
        `
        *,
        blog_categories (
          id,
          name,
          slug,
          color
        ),
        website_admins (
          id,
          name,
          email
        )
      `
      )
      .eq("slug", slug)
      .eq("market", market)
      .eq("language", language)
      .eq("status", "published")
      .single();

    if (error || !article) return null;
    return article;
  }

  /**
   * Get the default language for a market
   */
  private async getMarketDefaultLanguage(
    market: BlogMarketType
  ): Promise<BlogLanguageType | null> {
    const { data: marketInfo, error } = await supabase
      .from("blog_markets")
      .select("language_code")
      .eq("market_code", market)
      .eq("is_default_for_market", true)
      .single();

    if (error || !marketInfo) return null;
    return marketInfo.language_code as BlogLanguageType;
  }

  /**
   * Find translated content using the translation_keys system
   */
  private async findTranslatedContent(
    slug: string,
    language: BlogLanguageType
  ): Promise<BlogArticle | null> {
    // First, find the base article
    const { data: baseArticle } = await supabase
      .from("blog_articles")
      .select("*")
      .eq("slug", slug)
      .eq("market", "global")
      .eq("language", "en")
      .eq("status", "published")
      .single();

    if (!baseArticle) return null;

    // Look for translations using translation_keys
    const { data: translations } = await supabase
      .from("translation_keys")
      .select(
        `
        *,
        translations!inner (
          language,
          content,
          is_approved
        )
      `
      )
      .like("key", `blog.article.%.${slug}`)
      .eq("translations.language", language)
      .eq("translations.is_approved", true);

    if (!translations || translations.length === 0) return null;

    // Build translated article
    const translatedArticle = { ...baseArticle };

    for (const translationKey of translations) {
      const translation = translationKey.translations[0];
      if (!translation) continue;

      const fieldType = translationKey.metadata?.field_type;
      switch (fieldType) {
        case "title":
          translatedArticle.title = translation.content;
          break;
        case "summary":
          translatedArticle.summary = translation.content;
          break;
        case "content":
          translatedArticle.content = translation.content;
          break;
      }
    }

    return translatedArticle;
  }

  /**
   * Get available markets and languages
   */
  async getAvailableMarkets(): Promise<
    Array<{
      marketCode: string;
      marketName: string;
      languageCode: string;
      languageName: string;
      urlPath: string;
      isActive: boolean;
    }>
  > {
    const { data: markets, error } = await supabase
      .from("blog_markets")
      .select("*")
      .eq("is_active", true)
      .order("display_order");

    if (error || !markets) return [];

    return markets.map((market) => ({
      marketCode: market.market_code,
      marketName: market.market_name,
      languageCode: market.language_code,
      languageName: market.language_name,
      urlPath: market.url_path,
      isActive: market.is_active,
    }));
  }

  /**
   * Get content fallback chain for debugging/transparency
   */
  getContentFallbackChain(
    market: BlogMarketType,
    language: BlogLanguageType
  ): Array<{
    market: BlogMarketType;
    language: BlogLanguageType;
    priority: number;
    description: string;
  }> {
    const chain = [];
    let priority = 1;

    // Priority 1: Exact match
    chain.push({
      market,
      language,
      priority: priority++,
      description: `Exact match: ${market} market in ${language}`,
    });

    // Priority 2: Market-specific in default language (if different)
    if (market !== "global") {
      const marketDefaultLang = this.getMarketDefaultLanguageSync(market);
      if (marketDefaultLang && marketDefaultLang !== language) {
        chain.push({
          market,
          language: marketDefaultLang,
          priority: priority++,
          description: `${market} market in default language (${marketDefaultLang})`,
        });
      }
    }

    // Priority 3: Global in requested language
    if (market !== "global") {
      chain.push({
        market: "global" as BlogMarketType,
        language,
        priority: priority++,
        description: `Global content in ${language}`,
      });
    }

    // Priority 4: Global in English (ultimate fallback)
    if (language !== "en") {
      chain.push({
        market: "global" as BlogMarketType,
        language: "en" as BlogLanguageType,
        priority: priority++,
        description: "Global content in English (fallback)",
      });
    }

    return chain;
  }

  /**
   * Synchronous version of getMarketDefaultLanguage for fallback chain
   */
  private getMarketDefaultLanguageSync(
    market: BlogMarketType
  ): BlogLanguageType | null {
    const defaults = {
      usa: "en" as BlogLanguageType,
      "be-fr": "fr" as BlogLanguageType,
      "be-nl": "nl" as BlogLanguageType,
      global: "en" as BlogLanguageType,
    };

    return defaults[market] || null;
  }

  /**
   * Check content availability across markets/languages
   */
  async checkContentAvailability(slug: string): Promise<{
    slug: string;
    availability: Array<{
      market: BlogMarketType;
      language: BlogLanguageType;
      available: boolean;
      isTranslation: boolean;
      hasTranslationKey: boolean;
    }>;
  }> {
    const markets: BlogMarketType[] = ["global", "usa", "be-fr", "be-nl"];
    const languages: BlogLanguageType[] = ["en", "fr", "nl"];
    const availability = [];

    for (const market of markets) {
      for (const language of languages) {
        // Check direct article availability
        const article = await this.findArticle(slug, market, language);

        // Check translation key availability
        const hasTranslationKey = await this.hasTranslationKey(slug, language);

        availability.push({
          market,
          language,
          available: !!article,
          isTranslation: article?.isTranslation || false,
          hasTranslationKey,
        });
      }
    }

    return {
      slug,
      availability,
    };
  }

  /**
   * Check if translation key exists for a slug and language
   */
  private async hasTranslationKey(
    slug: string,
    language: BlogLanguageType
  ): Promise<boolean> {
    const { data } = await supabase
      .from("translation_keys")
      .select(
        `
        translations!inner (language)
      `
      )
      .like("key", `blog.article.%.${slug}`)
      .eq("translations.language", language)
      .limit(1);

    return !!(data && data.length > 0);
  }
}

// Export singleton instance
export const contentResolutionService = new ContentResolutionService();
